/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
package com.hbis.modules.mis.biz.service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.hbis.common.entity.Page;
import com.hbis.common.lang.NumberUtils;
import com.hbis.common.mybatis.mapper.query.QueryType;
import com.hbis.common.service.CrudService;
import com.hbis.common.service.ServiceException;
import com.hbis.modules.mis.utils.LocalDateUtil;
import com.hbis.modules.sys.entity.User;
import com.hbis.modules.sys.utils.UserUtils;
import com.hbis.modules.tmis.dao.TmisM231Dao;
import com.hbis.modules.tmis.entity.TmisM230;
import com.hbis.modules.tmis.entity.TmisM231;
import com.hbis.modules.tmis.service.TmisM230Service;
import com.hbis.modules.tsys.service.TsysS100ParameterSetService;

/**
 * 铁水计量实绩Service
 * <AUTHOR>
 * @version 2025-06-26
 */
@Service
@Transactional(readOnly=true)
public class ME022Service extends CrudService<TmisM231Dao, TmisM231> {
	
	@Resource
	private TmisM230Service m230Service;
	@Resource
	private TsysS100ParameterSetService s100Service;
//	@Resource
//	private IntB09B02IronDynamicWeightService biz2SvcService;
	
	/**
	 * 获取单条数据
	 * @param tmisM231
	 * @return
	 */
	@Override
	public TmisM231 get(TmisM231 tmisM231) {
		return super.get(tmisM231);
	}
	
	public TmisM231 getByEntity(TmisM231 m231) {
		return super.dao.getByEntity(m231);
	}
	
	/**
	 * 查询分页数据
	 * @param tmisM231 查询条件
	 * @param tmisM231.page 分页对象
	 * @return
	 */
	@Override
	public Page<TmisM231> findPage(TmisM231 tmisM231) {
		return super.findPage(tmisM231);
	}
	
	public Page<TmisM230> findSelectPage(TmisM230 tmisM230) {
		return m230Service.findPage(tmisM230);
	}
	
	public static class WeightConfig {
		
		private final Double tareMaxLimit; // 最大皮重限制
		private final Double grossMinLimit; // 最小毛重限制
		private final Double diffLimit; 
		
		public WeightConfig(Double tareMaxLimit, 
								Double grossMinLimit, Double diffLimit) {
			this.tareMaxLimit = tareMaxLimit;
			this.grossMinLimit = grossMinLimit;
			this.diffLimit = diffLimit;
		}

		public Double getTareMaxLimit() {
			return tareMaxLimit;
		}

		public Double getGrossMinLimit() {
			return grossMinLimit;
		}

		public Double getDiffLimit() {
			return diffLimit;
		}
		
	}
	
	
	@Transactional(readOnly=false)
	public List<TmisM231> autoMatch(String ids) {
		List<String> idList = parseAndValidateIds(ids);

		MatchContext context = initializeMatchContext();
		List<TmisM231> resultList = Lists.newArrayList();

		for (String id : idList) {
			TmisM231 entity = getEntityById(id);
			MatchResult result = processEntity(entity, context);
			resultList.add(result.getEntity());
		}

		return resultList;
	}

	/**
	 * 解析和验证ID列表
	 */
	private List<String> parseAndValidateIds(String ids) {
		List<String> list = Splitter.on(",").omitEmptyStrings().trimResults()
				.splitToStream(ids)
				.distinct().collect(Collectors.toList());
		if (list == null || list.isEmpty()) {
			throw new ServiceException("没有需要匹配的数据");
		}
		return list;
	}

	/**
	 * 初始化匹配上下文
	 */
	private MatchContext initializeMatchContext() {
		Date sysDate = s100Service.getSysDate();
		User user = UserUtils.getUser();
		String userCode = user.getUserCode();
		WeightConfig weightConfig = new WeightConfig(180D, 130D, 160D);

		return new MatchContext(sysDate, userCode, weightConfig);
	}

	/**
	 * 根据ID获取实体
	 */
	private TmisM231 getEntityById(String id) {
		TmisM231 entity = new TmisM231(id);
		entity = get(entity);
		if (entity == null) {
			throw new ServiceException("数据不存在，操作失败！");
		}
		return entity;
	}

	/**
	 * 处理单个实体的匹配逻辑
	 */
	private MatchResult processEntity(TmisM231 entity, MatchContext context) {
		// 检查是否已丢弃或已匹配
		MatchResult earlyResult = checkEarlyExit(entity);
		if (earlyResult != null) {
			return earlyResult;
		}

		// 获取委托信息
		TmisM230 consignment = findConsignment(entity, context);

		// 根据车辆类型进行匹配
		VehicleType vehicleType = VehicleType.fromCode(entity.getIronmessagetype());
		switch (vehicleType) {
			case EMPTY:
				return processEmptyVehicle(entity, consignment, context);
			case LOADED:
				return processLoadedVehicle(entity, consignment, context);
			default:
				entity.setRemarks("未知车辆类型");
				return new MatchResult(entity, false);
		}
	}

	/**
	 * 检查是否需要提前退出（已丢弃或已匹配）
	 */
	private MatchResult checkEarlyExit(TmisM231 entity) {
		String ironmessagetype = entity.getIronmessagetype();
		String ismatch = entity.getIsmatch();

		if ("2".equals(ironmessagetype)) {
			entity.setRemarks("已丢弃");
			return new MatchResult(entity, false);
		}

		if ("1".equals(ismatch)) {
			entity.setRemarks("已匹配");
			return new MatchResult(entity, false);
		}

		return null;
	}

	/**
	 * 查找委托信息
	 */
	private TmisM230 findConsignment(TmisM231 entity, MatchContext context) {
		Date createDate = entity.getCreateDate();
		LocalDateTime localDateTime = LocalDateUtil.getLocalDateTime(createDate);
		LocalDateTime plusDateTime = localDateTime.plusMinutes(30);
		LocalDateTime minusDateTime = localDateTime.minusHours(1);

		TmisM230 m230 = new TmisM230();
		m230.getSqlMap().getWhere().disableAutoAddStatusWhere()
			.and("status", QueryType.EQ, "0")
			.and("comp_code", QueryType.EQ, "2030")
			.and("ladleno", QueryType.EQ_FORCE, entity.getLadleno())
			.and("consigntype", QueryType.EQ, entity.getIronmessagetype())
			.and("create_date", QueryType.GTE, minusDateTime)
			.and("create_date", QueryType.LTE, plusDateTime);
		m230.getSqlMap().getOrder().setOrderBy("a.create_date DESC");

		List<TmisM230> m230List = m230Service.findList(m230);
		return (m230List != null && !m230List.isEmpty()) ? m230List.get(0) : null;
	}

	/**
	 * 处理空车匹配逻辑
	 */
	private MatchResult processEmptyVehicle(TmisM231 entity, TmisM230 consignment, MatchContext context) {
		// 验证空车皮重
		if (!validateEmptyVehicleWeight(entity, context)) {
			return createDiscardResult(entity, "空车皮重大于最大皮重" + context.getWeightConfig().getTareMaxLimit() + "自动丢废", context);
		}

		// 查找对应的重车实绩
		List<TmisM231> loadedVehicles = findMatchingLoadedVehicles(entity, context);
		if (loadedVehicles.isEmpty()) {
			entity.setRemarks("无重车实绩数据未匹配");
			return new MatchResult(entity, false);
		}

		// 尝试匹配重车
		for (TmisM231 loadedVehicle : loadedVehicles) {
			if (tryMatchEmptyWithLoaded(entity, loadedVehicle, consignment, context)) {
				entity.setRemarks("自动匹配");
				return new MatchResult(entity, true);
			}
		}

		entity.setRemarks("无法匹配重车实绩");
		return new MatchResult(entity, false);
	}

	/**
	 * 处理重车匹配逻辑
	 */
	private MatchResult processLoadedVehicle(TmisM231 entity, TmisM230 consignment, MatchContext context) {
		// 验证重车毛重
		if (!validateLoadedVehicleWeight(entity, context)) {
			return createDiscardResult(entity, "重车毛重小于最小毛重" + context.getWeightConfig().getGrossMinLimit() + "自动丢废", context);
		}

		// 查找对应的空车实绩
		List<TmisM231> emptyVehicles = findMatchingEmptyVehicles(entity, context);
		if (emptyVehicles.isEmpty()) {
			entity.setRemarks("无空车实绩数据未匹配");
			return new MatchResult(entity, false);
		}

		// 尝试匹配空车
		for (TmisM231 emptyVehicle : emptyVehicles) {
			if (tryMatchLoadedWithEmpty(entity, emptyVehicle, consignment, context)) {
				entity.setRemarks("自动匹配");
				return new MatchResult(entity, true);
			}
		}

		entity.setRemarks("无法匹配空车实绩");
		return new MatchResult(entity, false);
	}

	/**
	 * 验证空车皮重
	 */
	private boolean validateEmptyVehicleWeight(TmisM231 entity, MatchContext context) {
		Double tareweight = entity.getTareweight();
		Double tareMaxLimit = context.getWeightConfig().getTareMaxLimit();
		return Double.compare(tareweight, tareMaxLimit) <= 0;
	}

	/**
	 * 验证重车毛重
	 */
	private boolean validateLoadedVehicleWeight(TmisM231 entity, MatchContext context) {
		Double grossweight = entity.getGrossweight();
		Double grossMinLimit = context.getWeightConfig().getGrossMinLimit();
		return Double.compare(grossweight, grossMinLimit) >= 0;
	}

	/**
	 * 查找匹配的重车实绩
	 */
	private List<TmisM231> findMatchingLoadedVehicles(TmisM231 emptyEntity, MatchContext context) {
		Date createDate = emptyEntity.getCreateDate();
		LocalDateTime localDateTime = LocalDateUtil.getLocalDateTime(createDate);
		LocalDateTime minusDateTime = localDateTime.minusHours(1);

		TmisM231 query = new TmisM231();
		query.getSqlMap().getWhere().disableAutoAddStatusWhere()
			.and("status", QueryType.EQ, "0")
			.and("comp_code", QueryType.EQ, "2030")
			.and("ladleno", QueryType.EQ_FORCE, emptyEntity.getLadleno())
			.and("ironmessagetype", QueryType.EQ, "1")
			.and("ismatch", QueryType.EQ, "0")
			.and("create_date", QueryType.GTE, minusDateTime)
			.and("create_date", QueryType.LTE, createDate);
		query.getSqlMap().getOrder().setOrderBy("a.create_date DESC");

		List<TmisM231> result = super.dao.findList(query);
		return result != null ? result : Lists.newArrayList();
	}

	/**
	 * 查找匹配的空车实绩
	 */
	private List<TmisM231> findMatchingEmptyVehicles(TmisM231 loadedEntity, MatchContext context) {
		Date createDate = loadedEntity.getCreateDate();
		LocalDateTime localDateTime = LocalDateUtil.getLocalDateTime(createDate);
		LocalDateTime plusDateTime = localDateTime.plusMinutes(30);

		TmisM231 query = new TmisM231();
		query.getSqlMap().getWhere().disableAutoAddStatusWhere()
			.and("status", QueryType.EQ, "0")
			.and("comp_code", QueryType.EQ, "2030")
			.and("ladleno", QueryType.EQ_FORCE, loadedEntity.getLadleno())
			.and("ironmessagetype", QueryType.EQ, "0")
			.and("ismatch", QueryType.EQ, "0")
			.and("create_date", QueryType.GTE, createDate)
			.and("create_date", QueryType.LTE, plusDateTime);
		query.getSqlMap().getOrder().setOrderBy("a.create_date DESC");

		List<TmisM231> result = super.dao.findList(query);
		return result != null ? result : Lists.newArrayList();
	}

	/**
	 * 尝试匹配空车和重车
	 */
	private boolean tryMatchEmptyWithLoaded(TmisM231 emptyEntity, TmisM231 loadedEntity,
			TmisM230 consignment, MatchContext context) {
		// 验证重车毛重
		if (!validateLoadedVehicleWeight(loadedEntity, context)) {
			discardEntity(loadedEntity, "重车毛重小于最小毛重" + context.getWeightConfig().getGrossMinLimit() + "自动丢废", context);
			return false;
		}

		// 计算净重并验证磅差
		double netWeight = NumberUtils.sub(loadedEntity.getGrossweight(), emptyEntity.getTareweight());
		if (!validateNetWeight(netWeight, context)) {
			discardEntity(loadedEntity, "重车净重大于磅差限制" + context.getWeightConfig().getDiffLimit() + "自动丢废", context);
			return false;
		}

		// 执行匹配更新
		updateMatchedEntities(emptyEntity, loadedEntity, consignment, netWeight, context, true);
		return true;
	}

	/**
	 * 尝试匹配重车和空车
	 */
	private boolean tryMatchLoadedWithEmpty(TmisM231 loadedEntity, TmisM231 emptyEntity,
			TmisM230 consignment, MatchContext context) {
		// 验证空车皮重
		if (!validateEmptyVehicleWeight(emptyEntity, context)) {
			discardEntity(emptyEntity, "空车皮重大于最大皮重" + context.getWeightConfig().getTareMaxLimit() + "自动丢废", context);
			return false;
		}

		// 计算净重并验证磅差
		double netWeight = NumberUtils.sub(loadedEntity.getGrossweight(), emptyEntity.getTareweight());
		if (!validateNetWeight(netWeight, context)) {
			discardEntity(emptyEntity, "重车净重大于磅差限制" + context.getWeightConfig().getDiffLimit() + "自动丢废", context);
			return false;
		}

		// 执行匹配更新
		updateMatchedEntities(emptyEntity, loadedEntity, consignment, netWeight, context, false);
		return true;
	}

	/**
	 * 验证净重是否在磅差限制内
	 */
	private boolean validateNetWeight(double netWeight, MatchContext context) {
		Double diffLimit = context.getWeightConfig().getDiffLimit();
		return Double.compare(netWeight, diffLimit) <= 0;
	}

	/**
	 * 更新匹配的实体
	 */
	private void updateMatchedEntities(TmisM231 emptyEntity, TmisM231 loadedEntity,
			TmisM230 consignment, double netWeight, MatchContext context, boolean isEmptyFirst) {

		// 更新空车状态
		TmisM231 emptyUpdate = new TmisM231(emptyEntity.getId());
		emptyUpdate.setIsmatch("1");
		emptyUpdate.setUpdateBy(context.getUserCode());
		emptyUpdate.setUpdateDate(context.getSysDate());
		if (consignment != null) {
			updateConsignmentInfo(emptyUpdate, consignment, true);
		}
		super.update(emptyUpdate);

		// 更新重车状态
		TmisM231 loadedUpdate = new TmisM231(loadedEntity.getId());
		loadedUpdate.setIsmatch("1");
		loadedUpdate.setTareweight(emptyEntity.getTareweight());
		loadedUpdate.setNetweight(netWeight);
		loadedUpdate.setUpdateBy(context.getUserCode());
		loadedUpdate.setUpdateDate(context.getSysDate());
		loadedUpdate.setRemarks("自动匹配");

		// 设置空车相关信息
		copyEmptyVehicleInfo(loadedUpdate, emptyEntity);
		if (consignment != null) {
			updateConsignmentInfo(loadedUpdate, consignment, false);
		}
		super.update(loadedUpdate);
	}

	/**
	 * 复制空车信息到重车记录
	 */
	private void copyEmptyVehicleInfo(TmisM231 target, TmisM231 emptyEntity) {
		target.setEmptyconsignnum(emptyEntity.getEmptyconsignnum());
		target.setEmptyserialnumber(emptyEntity.getEmptyserialnumber());
		target.setEmptyconsigntime(emptyEntity.getEmptyconsigntime());
		target.setTaretime(emptyEntity.getTaretime());
		target.setTareshift(emptyEntity.getTareshift());
		target.setTarecrew(emptyEntity.getTarecrew());
		target.setTareempno(emptyEntity.getTareempno());
		target.setEmptyspeed(emptyEntity.getEmptyspeed());
	}

	/**
	 * 更新委托信息
	 */
	private void updateConsignmentInfo(TmisM231 target, TmisM230 consignment, boolean isEmpty) {
		if (isEmpty) {
			target.setHeavyconsignnum(consignment.getConsignnum());
			target.setHeavyserialnumber(consignment.getSerialnumber());
		} else {
			target.setEmptyconsignnum(consignment.getConsignnum());
			target.setEmptyserialnumber(consignment.getSerialnumber());
		}

		target.setCarframe(consignment.getCarframe());
		target.setCartype(consignment.getCartype());
		target.setStovenum(consignment.getStovenum());
		target.setStovecode(consignment.getStovecode());
		target.setStovename(consignment.getStovename());
		target.setTapholecode(consignment.getTapholecode());
		target.setTapholename(consignment.getTapholename());
		target.setTrackcode(consignment.getTrackcode());
		target.setTrackname(consignment.getTrackname());
		target.setHeavytarget(consignment.getHeavytarget());
	}

	/**
	 * 丢弃实体
	 */
	private void discardEntity(TmisM231 entity, String remarks, MatchContext context) {
		TmisM231 update = new TmisM231(entity.getId());
		update.setRemarks(remarks);
		update.setIronmessagetype("2"); // 丢弃
		update.setUpdateBy(context.getUserCode());
		update.setUpdateDate(context.getSysDate());
		super.update(update);
	}

	/**
	 * 创建丢弃结果
	 */
	private MatchResult createDiscardResult(TmisM231 entity, String remarks, MatchContext context) {
		discardEntity(entity, remarks, context);
		entity.setRemarks(remarks);
		return new MatchResult(entity, false);
	}

	/**
	 * 车辆类型枚举
	 */
	private enum VehicleType {
		EMPTY("0"),
		LOADED("1"),
		DISCARDED("2");

		private final String code;

		VehicleType(String code) {
			this.code = code;
		}

		public static VehicleType fromCode(String code) {
			for (VehicleType type : values()) {
				if (type.code.equals(code)) {
					return type;
				}
			}
			return DISCARDED;
		}
	}

	/**
	 * 匹配上下文
	 */
	private static class MatchContext {
		private final Date sysDate;
		private final String userCode;
		private final WeightConfig weightConfig;

		public MatchContext(Date sysDate, String userCode, WeightConfig weightConfig) {
			this.sysDate = sysDate;
			this.userCode = userCode;
			this.weightConfig = weightConfig;
		}

		public Date getSysDate() { return sysDate; }
		public String getUserCode() { return userCode; }
		public WeightConfig getWeightConfig() { return weightConfig; }
	}

	/**
	 * 匹配结果
	 */
	private static class MatchResult {
		private final TmisM231 entity;
		private final boolean matched;

		public MatchResult(TmisM231 entity, boolean matched) {
			this.entity = entity;
			this.matched = matched;
		}

		public TmisM231 getEntity() { return entity; }
		public boolean isMatched() { return matched; }
	}

}