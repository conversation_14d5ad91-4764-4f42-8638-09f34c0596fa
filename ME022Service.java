/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 */
package com.hbis.modules.mis.biz.service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.hbis.common.entity.Page;
import com.hbis.common.lang.NumberUtils;
import com.hbis.common.mybatis.mapper.query.QueryType;
import com.hbis.common.service.CrudService;
import com.hbis.common.service.ServiceException;
import com.hbis.modules.mis.utils.LocalDateUtil;
import com.hbis.modules.sys.entity.User;
import com.hbis.modules.sys.utils.UserUtils;
import com.hbis.modules.tmis.dao.TmisM231Dao;
import com.hbis.modules.tmis.entity.TmisM230;
import com.hbis.modules.tmis.entity.TmisM231;
import com.hbis.modules.tmis.service.TmisM230Service;
import com.hbis.modules.tsys.service.TsysS100ParameterSetService;

/**
 * 铁水计量实绩Service
 * <AUTHOR>
 * @version 2025-06-26
 */
@Service
@Transactional(readOnly=true)
public class ME022Service extends CrudService<TmisM231Dao, TmisM231> {
	
	@Resource
	private TmisM230Service m230Service;
	@Resource
	private TsysS100ParameterSetService s100Service;
//	@Resource
//	private IntB09B02IronDynamicWeightService biz2SvcService;
	
	/**
	 * 获取单条数据
	 * @param tmisM231
	 * @return
	 */
	@Override
	public TmisM231 get(TmisM231 tmisM231) {
		return super.get(tmisM231);
	}
	
	public TmisM231 getByEntity(TmisM231 m231) {
		return super.dao.getByEntity(m231);
	}
	
	/**
	 * 查询分页数据
	 * @param tmisM231 查询条件
	 * @param tmisM231.page 分页对象
	 * @return
	 */
	@Override
	public Page<TmisM231> findPage(TmisM231 tmisM231) {
		return super.findPage(tmisM231);
	}
	
	public Page<TmisM230> findSelectPage(TmisM230 tmisM230) {
		return m230Service.findPage(tmisM230);
	}
	
	public static class WeightConfig {
		
		private final Double tareMaxLimit; // 最大皮重限制
		private final Double grossMinLimit; // 最小毛重限制
		private final Double diffLimit; 
		
		public WeightConfig(Double tareMaxLimit, 
								Double grossMinLimit, Double diffLimit) {
			this.tareMaxLimit = tareMaxLimit;
			this.grossMinLimit = grossMinLimit;
			this.diffLimit = diffLimit;
		}

		public Double getTareMaxLimit() {
			return tareMaxLimit;
		}

		public Double getGrossMinLimit() {
			return grossMinLimit;
		}

		public Double getDiffLimit() {
			return diffLimit;
		}
		
	}
	
	
	@Transactional(readOnly=false)
	public List<TmisM231> autoMatch(String ids) {
		List<String> list = Splitter.on(",").omitEmptyStrings().trimResults()
				.splitToStream(ids)
				.distinct().collect(Collectors.toList());
		if (list == null || list.isEmpty()) {
			throw new ServiceException("没有需要匹配的数据");
		}
		
		List<TmisM231> m231List = Lists.newArrayList();
		Date sysDate = s100Service.getSysDate();
		User user = UserUtils.getUser();
		String userCode = user.getUserCode();
		
		WeightConfig weightConfig = new WeightConfig(180D, 130D, 160D);
		Double grossMinLimit = weightConfig.getGrossMinLimit();
		Double tareMaxLimit = weightConfig.getTareMaxLimit();
		Double diffLimit = weightConfig.getDiffLimit();
		
		String matchFlag = "1"; // 已匹配
		String delFlag = "2"; // 丢弃
		
		for (String id : list) {
			TmisM231 entity = new TmisM231(id);
			TmisM231 update = new TmisM231(id);
			// 最新数据
			entity = get(entity);
			if (entity == null) {
				throw new ServiceException("数据不存在，操作失败！");
			}
			
			Date createDate = entity.getCreateDate();
			LocalDateTime localDateTime = LocalDateUtil.getLocalDateTime(createDate);
			LocalDateTime plusDateTime = localDateTime.plusMinutes(30);
			LocalDateTime minusDateTime = localDateTime.minusHours(1);
			
			// 皮重和毛重
			Double tareweight = entity.getTareweight();
			Double grossweight = entity.getGrossweight();
			// 车辆状态 0：空车，1：重车，2：丢弃
			String ironmessagetype = entity.getIronmessagetype();
			// 匹配状态 0 未匹配 1 已匹配
			String ismatch = entity.getIsmatch();
			// 罐号
			String ladleno = entity.getLadleno();
			
			boolean empty_m231 = StringUtils.equals("0", ironmessagetype);
			boolean load_m231 = StringUtils.equals("1", ironmessagetype);
			boolean del_m231 = StringUtils.equals("2", ironmessagetype);
			
			if (del_m231) {
				entity.setRemarks("已丢弃");
				m231List.add(entity);
				continue;
			}
			
			if ("1".equals(ismatch)) {
				entity.setRemarks("已匹配");
				m231List.add(entity);
				continue;
			} 
			
			// 委托
			TmisM230 m230 = new TmisM230();
			m230.getSqlMap().getWhere().disableAutoAddStatusWhere()
				.and("status", QueryType.EQ, "0")
				.and("comp_code", QueryType.EQ, "2030")
				.and("ladleno", QueryType.EQ_FORCE, ladleno)
				.and("consigntype", QueryType.EQ, ironmessagetype)
				.and("create_date", QueryType.GTE, minusDateTime)
				.and("create_date", QueryType.LTE, plusDateTime);
			m230.getSqlMap().getOrder().setOrderBy("a.create_date DESC");
			List<TmisM230> m230List = m230Service.findList(m230);
			if (m230List == null || m230List.isEmpty()) {
				entity.setRemarks("无空车委托数据未匹配");
				m231List.add(entity);
			} else {
				// 获取最新的委托
				m230 = m230List.get(0);
				String consignnum = m230.getConsignnum();
				String serialnumber = m230.getSerialnumber();
				String carframe = m230.getCarframe();
				String cartype = m230.getCartype();
				String stovenum = m230.getStovenum();
				String stovecode = m230.getStovecode();
				String stovename = m230.getStovename();
				String tapholecode = m230.getTapholecode();
				String tapholename = m230.getTapholename();
				String trackcode = m230.getTrackcode();
				String trackname = m230.getTrackname();
				String consigntype = m230.getConsigntype();
				String heavytarget = m230.getHeavytarget();
				
				boolean empty_m230 = StringUtils.equals("0", consigntype);
				boolean load_m230 = StringUtils.equals("1", consigntype);
				
				if (load_m231 && load_m230) {
					update.setEmptyconsignnum(consignnum);
					update.setEmptyserialnumber(serialnumber);
				}
				if (empty_m231 && empty_m230) {
					update.setHeavyconsignnum(consignnum);
					update.setHeavyserialnumber(serialnumber);
				}
				update.setCarframe(carframe);
				update.setCartype(cartype);
				update.setStovenum(stovenum);
				update.setStovecode(stovecode);
				update.setStovename(stovename);
				update.setTapholecode(tapholecode);
				update.setTapholename(tapholename);
				update.setTrackcode(trackcode);
				update.setTrackname(trackname);
				update.setHeavytarget(heavytarget);
			}
			
			if (empty_m231) {
				// 空车
				String emptyconsignnum = entity.getEmptyconsignnum();
				String emptyserialnumber = entity.getEmptyserialnumber();
				Date emptyconsigntime = entity.getEmptyconsigntime();
				Date taretime = entity.getTaretime();
				String tareshift = entity.getTareshift();
				String tarecrew = entity.getTarecrew();
				String tareempno = entity.getTareempno();
				Double emptyspeed = entity.getEmptyspeed();
				
				int empty_result_tare_empty = Double.compare(tareweight, tareMaxLimit);
				if (empty_result_tare_empty > 0) {
				    String remarks = "空车皮重大于最大皮重" 
							+ String.valueOf(tareMaxLimit) + "自动丢废";
				    update.setRemarks(remarks);
				    update.setIronmessagetype(delFlag);
				    update.setUpdateBy(userCode);
					update.setUpdateDate(sysDate);
					super.update(update);
					
					entity.setRemarks(remarks);
					m231List.add(entity);
					continue;
				}
				
				// 重车实绩
				TmisM231 loadM321 = new TmisM231();	
				loadM321.getSqlMap().getWhere().disableAutoAddStatusWhere()
					.and("status", QueryType.EQ, "0")
					.and("comp_code", QueryType.EQ, "2030")
					.and("ladleno", QueryType.EQ_FORCE, ladleno)
					.and("ironmessagetype", QueryType.EQ, "1")
					.and("ismatch", QueryType.EQ, "0")
					.and("create_date", QueryType.GTE, minusDateTime)
					.and("create_date", QueryType.LTE, createDate);
				loadM321.getSqlMap().getOrder().setOrderBy("a.create_date DESC");
				List<TmisM231> loadM321List = super.dao.findList(loadM321);
				if (loadM321List == null || loadM321List.isEmpty()) {
					entity.setRemarks("无重车实绩数据未匹配");
					m231List.add(entity);
					continue;	
				}
				for (TmisM231 loadM231Temp : m231List) {
					String id_load = loadM231Temp.getId();
					TmisM231 emptyLoadUpdate = new TmisM231(id_load);
					
					// 设置重车实绩
					Double empty_grossweight_load = loadM231Temp.getGrossweight();
					int empty_result_gross_load = Double.compare(empty_grossweight_load, grossMinLimit);
					// 重车毛重小于最小毛重
					if (empty_result_gross_load < 0) {
						String remarks = "重车毛重小于最小毛重" 
								+ String.valueOf(grossMinLimit) + "自动丢废";
						emptyLoadUpdate.setRemarks(remarks);
						emptyLoadUpdate.setIronmessagetype(delFlag);
						emptyLoadUpdate.setUpdateBy(userCode);
						emptyLoadUpdate.setUpdateDate(sysDate);
						super.update(emptyLoadUpdate);
						continue;
					}
					// 净重
					double empty_calc_net = NumberUtils.sub(empty_grossweight_load, tareweight);
					// 磅差
					int empty_diff_result = Double.compare(empty_calc_net, diffLimit);
					if (empty_diff_result > 0) {
						String remarks = "重车净重大于磅差限制" 
								+ String.valueOf(diffLimit) + "自动丢废";
						emptyLoadUpdate.setRemarks(remarks);
						emptyLoadUpdate.setIronmessagetype(delFlag);
						emptyLoadUpdate.setUpdateBy(userCode);
						emptyLoadUpdate.setUpdateDate(sysDate);
						super.update(emptyLoadUpdate);
						continue;
					}
					
					// 设置匹配数据空车状态
					update.setIsmatch(matchFlag);
					update.setUpdateBy(userCode);
					update.setUpdateDate(sysDate);
					super.update(update);
					
					// 设置重车实绩
					emptyLoadUpdate.setIsmatch(matchFlag);
					emptyLoadUpdate.setTareweight(tareweight);
					emptyLoadUpdate.setNetweight(empty_calc_net);
					emptyLoadUpdate.setEmptyconsignnum(emptyconsignnum);
					emptyLoadUpdate.setEmptyserialnumber(emptyserialnumber);
					emptyLoadUpdate.setEmptyconsigntime(emptyconsigntime);
					emptyLoadUpdate.setTaretime(taretime);
					emptyLoadUpdate.setTareshift(tareshift);
					emptyLoadUpdate.setTarecrew(tarecrew);
					emptyLoadUpdate.setTareempno(tareempno);
					emptyLoadUpdate.setEmptyspeed(emptyspeed);
					emptyLoadUpdate.setUpdateBy(userCode);
					emptyLoadUpdate.setUpdateDate(sysDate);
					emptyLoadUpdate.setRemarks("自动匹配");
					super.update(emptyLoadUpdate);
					// 新增接口表数据
//					IntB09B02IronDynamicWeight biz2Svc = new IntB09B02IronDynamicWeight();
//					biz2Svc.setN("N");
//					biz2SvcService.insert(biz2Svc);
					
					break;
				}
			}
			
			if (load_m231) {
				// 重车
				int load_result_gross_load = Double.compare(grossweight, grossMinLimit);
				if (load_result_gross_load < 0) {
				    String remarks = "重车毛重小于最小毛重" 
									+ String.valueOf(grossMinLimit) + "自动丢废";
				    update.setRemarks(remarks);
				    update.setIronmessagetype(delFlag);
					update.setUpdateBy(userCode);
					update.setUpdateDate(sysDate);
					super.update(update);
					
					entity.setRemarks(remarks);
					m231List.add(entity);
					continue;
				}
				
				TmisM231 emptyM321 = new TmisM231();	
				emptyM321.getSqlMap().getWhere().disableAutoAddStatusWhere()
					.and("status", QueryType.EQ, "0")
					.and("comp_code", QueryType.EQ, "2030")
					.and("ladleno", QueryType.EQ_FORCE, ladleno)
					.and("ironmessagetype", QueryType.EQ, "0")
					.and("ismatch", QueryType.EQ, "0")
					.and("create_date", QueryType.GTE, createDate)
					.and("create_date", QueryType.LTE, plusDateTime);
				emptyM321.getSqlMap().getOrder().setOrderBy("a.create_date DESC");
				List<TmisM231> emptyM321List = super.dao.findList(emptyM321);
				if (emptyM321List == null || emptyM321List.isEmpty()) {
					entity.setRemarks("无空车实绩数据未匹配");
					m231List.add(entity);
					continue;	
				}
				for (TmisM231 emptyM321Temp : m231List) {
					String id_empty = emptyM321Temp.getId();
					TmisM231 loadEmptyUpdate = new TmisM231(id_empty);
					
					// 设置重车实绩
					Double load_tareweight_empty = emptyM321Temp.getTareweight();
					int load_result_tare_empty = Double.compare(load_tareweight_empty, tareMaxLimit);
					if (load_result_tare_empty > 0) {
					    String remarks = "空车皮重大于最大皮重" 
								+ String.valueOf(tareMaxLimit) + "自动丢废";
					    loadEmptyUpdate.setRemarks(remarks);
					    loadEmptyUpdate.setIronmessagetype(delFlag);
					    loadEmptyUpdate.setUpdateBy(userCode);
						loadEmptyUpdate.setUpdateDate(sysDate);
						super.update(loadEmptyUpdate);
						
						entity.setRemarks(remarks);
						m231List.add(entity);
						continue;
					}
					// 净重
					double load_calc_net = NumberUtils.sub(grossweight, load_tareweight_empty);
					// 磅差
					int load_diff_result = Double.compare(load_calc_net, diffLimit);
					if (load_diff_result > 0) {
						String remarks = "重车净重大于磅差限制" 
								+ String.valueOf(diffLimit) + "自动丢废";
						loadEmptyUpdate.setRemarks(remarks);
						loadEmptyUpdate.setIronmessagetype(delFlag);
						loadEmptyUpdate.setUpdateBy(userCode);
						loadEmptyUpdate.setUpdateDate(sysDate);
						super.update(loadEmptyUpdate);
						continue;
					}
					
					String emptyconsignnum = emptyM321Temp.getEmptyconsignnum();
					String emptyserialnumber = emptyM321Temp.getEmptyserialnumber();
					Date emptyconsigntime = emptyM321Temp.getEmptyconsigntime();
					Date taretime = emptyM321Temp.getTaretime();
					String tareshift = emptyM321Temp.getTareshift();
					String tarecrew = emptyM321Temp.getTarecrew();
					String tareempno = emptyM321Temp.getTareempno();
					Double emptyspeed = emptyM321Temp.getEmptyspeed();
					
					// 设置匹配数据空车状态
					loadEmptyUpdate.setIsmatch(matchFlag);
					loadEmptyUpdate.setUpdateBy(userCode);
					loadEmptyUpdate.setUpdateDate(sysDate);
					super.update(loadEmptyUpdate);
					
					// 设置重车实绩
					update.setIsmatch(matchFlag);
					update.setTareweight(tareweight);
					update.setNetweight(load_calc_net);
					update.setEmptyconsignnum(emptyconsignnum);
					update.setEmptyserialnumber(emptyserialnumber);
					update.setEmptyconsigntime(emptyconsigntime);
					update.setTaretime(taretime);
					update.setTareshift(tareshift);
					update.setTarecrew(tarecrew);
					update.setTareempno(tareempno);
					update.setEmptyspeed(emptyspeed);
					update.setUpdateBy(userCode);
					update.setUpdateDate(sysDate);
					update.setRemarks("自动匹配");
					super.update(update);
					// 新增接口表数据
//					IntB09B02IronDynamicWeight biz2Svc = new IntB09B02IronDynamicWeight();
//					biz2Svc.setN("N");
//					biz2SvcService.insert(biz2Svc);
					
					break;
				}
			}
//			// 接口表数据
//			IntB09B02IronDynamicWeight biz2Svc = new IntB09B02IronDynamicWeight();
//			biz2Svc.setN("N");
//			biz2SvcService.insert(biz2Svc);
		}
		
		return m231List;
		
	}

	
}